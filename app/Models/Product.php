<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class Product extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'user_id',
        'uuid',
        'vendor_id',
        'category_id',
        'sub_category_id',
        'class_id',
        'sub_class_id',
        'brand_id',
        'vendor_sku',
        'system_sku',
        'barcode',
        'model_number',
        'title_en',
        'title_ar',
        'slug',
        'short_name',
        'short_description_en',
        'short_description_ar',
        'description_en',
        'description_ar',
        'key_ingredients',
        'supplement_image',
        'usage_instruction_en',
        'usage_instruction_ar',
        'user_group_id',
        'net_weight',
        'net_weight_unit_id',
        'formulation_id',
        'servings',
        'flavour_id',
        'is_variant',
        'regular_price',
        'offer_price',
        'member_price',
        'wholesale_price',
        'vip_price',
        'pricing_tiers',
        'enable_member_pricing',
        'vat_tax',
        'discount_start_date',
        'discount_end_date',
        'approx_commission',
        'dietary_need_ids',
        'is_vegan',
        'is_vegetarian',
        'is_halal',
        'allergen_info_ids',
        'storage_conditions',
        'vat_tax_utl',
        'regulatory_product_registration',
        'country_of_origin',
        'is_returnable',
        'warranty',
        'bbe_date',
        'fulfillment_id',
        'package_length',
        'package_width',
        'package_height',
        'package_weight',
        'is_active',
        'is_approved',
        'status',
        'variant_setup'
    ];

    

    protected $appends = [
        'main_image_url',
        'supplement_image_url',
    ];

    protected $casts = [
        'is_variant'     => 'boolean',
        'is_vegan'       => 'boolean',
        'is_vegetarian'  => 'boolean',
        'is_halal'       => 'boolean',
        'is_active'      => 'boolean',
        'is_approved'    => 'boolean',
        'discount_start_date' => 'datetime',
        'discount_end_date'   => 'datetime',
        'bbe_date'            => 'date',
        'regular_price'       => 'decimal:2',
        'offer_price'         => 'decimal:2',
        'member_price'        => 'decimal:2',
        'wholesale_price'     => 'decimal:2',
        'vip_price'           => 'decimal:2',
        'enable_member_pricing' => 'boolean',
        'pricing_tiers'       => 'array',
        'approx_commission'   => 'decimal:2',
        'shipping_fee'        => 'decimal:2',
        'package_length'      => 'decimal:2',
        'package_width'       => 'decimal:2',
        'package_height'      => 'decimal:2',
        'package_weight'      => 'decimal:2',
        'dietary_need_ids'    => 'array',
        'allergen_info_ids'   => 'array',
        // Dropdown option fields - now store dropdown option IDs as integers
        'storage_conditions'  => 'integer',
        'country_of_origin'   => 'integer',
        'is_returnable'       => 'integer',
        'warranty'            => 'integer',
        'variant_setup'      => 'array',
    ];

    // Relationships
    public function user()
    {
        return $this->belongsTo(User::class);
    }
    public function vendor()
    {
        return $this->belongsTo(Vendor::class);
    }
    public function category()
    {
        return $this->belongsTo(Category::class);
    }
    public function subCategory()
    {
        return $this->belongsTo(Category::class, 'sub_category_id');
    }
    public function productClass()
    {
        return $this->belongsTo(ProductClass::class, 'class_id');
    }
    public function subClass()
    {
        return $this->belongsTo(ProductClass::class, 'sub_class_id');
    }
    public function brand()
    {
        return $this->belongsTo(Brand::class);
    }
    public function userGroup()
    {
        return $this->belongsTo(DropdownOption::class, 'user_group_id','id');
    }

    public function netWeightUnit()
    {
        return $this->belongsTo(DropdownOption::class, 'net_weight_unit_id', 'id');
    }
    public function formulation()
    {
        return $this->belongsTo(DropdownOption::class, 'formulation_id', 'id');
    }
    public function flavour()
    {
        return $this->belongsTo(DropdownOption::class, 'flavour_id', 'id');
    }

    public function storageConditions()
    {
        return $this->belongsTo(DropdownOption::class, 'storage_conditions', 'id');
    }

    public function countryOfOrigin()
    {
        return $this->belongsTo(DropdownOption::class, 'country_of_origin', 'id');
    }

    public function returnPolicy()
    {
        return $this->belongsTo(DropdownOption::class, 'is_returnable', 'id');
    }

    public function warranty()
    {
        return $this->belongsTo(DropdownOption::class, 'warranty', 'id');
    }

    public function fulfillment()
    {
        return $this->belongsTo(ProductFulfillment::class, 'fulfillment_id', 'id');
    }
    public function productMedia()
    {
        return $this->hasMany(ProductMedia::class);
    }
    public function productSeo()
    {
        return $this->hasOne(ProductSeo::class);
    }
    public function productFaqs()
    {
        return $this->hasMany(ProductFaq::class);
    }

    public function productVariants()
    {
        return $this->hasMany(ProductVariant::class);
    }
    public function inventory()
    {
        return $this->hasOne(Inventory::class);
    }

    public function reviews()
    {
        return $this->hasMany(Review::class);
    }

    public function publishedReviews()
    {
        return $this->hasMany(Review::class)->published();
    }

    public function getAverageRatingAttribute()
    {
        return Review::getAverageRatingForProduct($this->id);
    }

    public function getReviewsCountAttribute()
    {
        return Review::getReviewCountForProduct($this->id);
    }

    public function getMainImageUrlAttribute()
    {
        $primary = $this->productMedia()->where('is_primary', true)->first();
        return $primary ? config('filesystems.disks.s3.url') . '/' . $primary->path : null;
    }

    public function getSupplementImageUrlAttribute()
    {
        if ($this->supplement_image) {
            return config('filesystems.disks.s3.url') . '/' . $this->supplement_image;
        }
        return null;
    }

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($product) {
            // Generate UUID for MySQL databases (PostgreSQL handles this in migration)
            if (empty($product->uuid) && DB::getDriverName() !== 'pgsql') {
                $product->uuid = (string) Str::uuid();
            }

            if (empty($product->system_sku)) {
                $product->system_sku = 'SKU-' . uniqid();
            }

            // Generate slug from title_en if not provided
            if (empty($product->slug) && !empty($product->title_en)) {
                $product->slug = static::generateUniqueSlug($product->title_en);
            }

            if (Auth::check()) {
                $user = Auth::user();
                if ($user->hasRole('vendor') && empty($product->vendor_id)) {
                    $product->vendor_id = $user->vendor_id ?? null;
                }
            }
        });

        static::updating(function ($product) {
            // Regenerate slug if title_en changed and slug is empty
            if ($product->isDirty('title_en') && empty($product->slug) && !empty($product->title_en)) {
                $product->slug = static::generateUniqueSlug($product->title_en, $product->id);
            }
        });
    }

    /**
     * Generate a unique slug for the product
     *
     * @param string $title
     * @param int|null $excludeId
     * @return string
     */
    public static function generateUniqueSlug(string $title, ?int $excludeId = null): string
    {
        $baseSlug = Str::slug($title);
        $slug = $baseSlug;
        $counter = 1;

        // Check if slug already exists and append number if needed
        while (static::slugExists($slug, excludeId: $excludeId)) {
            $slug = "{$baseSlug}-{$counter}";
            $counter++;
        }

        return $slug;
    }

    /**
     * Check if slug exists in database
     *
     * @param string $slug
     * @param int|null $excludeId
     * @return bool
     */
    private static function slugExists(string $slug, ?int $excludeId = null): bool
    {
        $query = static::where('slug', $slug);

        if ($excludeId) {
            $query->where('id', '!=', $excludeId);
        }

        return $query->exists();
    }
}
